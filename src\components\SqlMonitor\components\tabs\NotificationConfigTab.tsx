import React from 'react';
import { Card, Button, Space } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';

// 导入重构后的模块
import type { AlertSend } from '../../types';
import { formStyles } from '../../styles';

interface NotificationConfigTabProps {
  alertSends: AlertSend[];
  onAlertSendsChange: (alertSends: AlertSend[]) => void;
  onAddAlertSend: () => void;
  onEditAlertSend: (index: number) => void;
  onSelectAlertSend: () => void;
}

/**
 * 告警发送配置标签页组件
 * 管理告警发送方式的配置
 */
const NotificationConfigTab: React.FC<NotificationConfigTabProps> = ({ alertSends, onAlertSendsChange, onAddAlertSend, onEditAlertSend, onSelectAlertSend }) => {
  const handleDeleteAlertSend = (index: number) => {
    const newAlertSends = alertSends.filter((_, i) => i !== index);
    onAlertSendsChange(newAlertSends);
  };

  const renderAlertSendItem = (alertSend: AlertSend, index: number) => {
    return (
      <div
        key={alertSend.id}
        style={{
          position: 'relative',
          backgroundColor: '#fff',
          border: '1px solid #f0f0f0',
          borderRadius: '12px',
          padding: '20px',
          boxShadow: '0 2px 4px rgba(0,0,0,0.02)',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          cursor: 'pointer',
          overflow: 'hidden',
        }}
        onMouseEnter={e => {
          e.currentTarget.style.transform = 'translateY(-2px)';
          e.currentTarget.style.boxShadow = '0 8px 24px rgba(0,0,0,0.12)';
          e.currentTarget.style.borderColor = alertSend.receive_type === 'kafka' ? '#1890ff' : '#52c41a';
        }}
        onMouseLeave={e => {
          e.currentTarget.style.transform = 'translateY(0)';
          e.currentTarget.style.boxShadow = '0 2px 4px rgba(0,0,0,0.02)';
          e.currentTarget.style.borderColor = '#f0f0f0';
        }}
      >
        {/* 顶部装饰条 */}
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '4px',
            background: `linear-gradient(90deg, ${alertSend.receive_type === 'kafka' ? '#1890ff, #40a9ff' : '#52c41a, #73d13d'})`,
          }}
        />

        {/* 操作按钮 - 右上角 */}
        <div style={{ position: 'absolute', top: '16px', right: '16px' }}>
          <Button
            type="text"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={e => {
              e.stopPropagation();
              handleDeleteAlertSend(index);
            }}
            style={{
              borderRadius: '50%',
              width: '28px',
              height: '28px',
              padding: 0,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          />
        </div>

        {/* 类型徽章 */}
        <div
          style={{
            display: 'inline-flex',
            alignItems: 'center',
            padding: '4px 12px',
            borderRadius: '16px',
            backgroundColor: alertSend.receive_type === 'kafka' ? '#e6f7ff' : '#f6ffed',
            border: `1px solid ${alertSend.receive_type === 'kafka' ? '#91d5ff' : '#b7eb8f'}`,
            marginBottom: '16px',
          }}
        >
          <span
            style={{
              color: alertSend.receive_type === 'kafka' ? '#1890ff' : '#52c41a',
              fontWeight: '600',
              fontSize: '12px',
              textTransform: 'uppercase',
              letterSpacing: '0.5px',
            }}
          >
            {alertSend.receive_type}
          </span>
        </div>

        {/* 名称 */}
        <div style={{ marginBottom: '12px', paddingRight: '40px' }}>
          <h4
            style={{
              margin: 0,
              fontSize: '16px',
              fontWeight: '600',
              color: '#262626',
              lineHeight: '1.4',
              wordBreak: 'break-word',
            }}
          >
            {alertSend.name}
          </h4>
        </div>

        {/* 配置信息 */}
        <div style={{ marginBottom: '8px' }}>
          {!alertSend.config ? (
            <div
              style={{
                padding: '12px',
                backgroundColor: '#fafafa',
                borderRadius: '8px',
                border: '1px dashed #d9d9d9',
                textAlign: 'center',
                color: '#bfbfbf',
                fontSize: '13px',
              }}
            >
              暂无配置信息
            </div>
          ) : (
            <div style={{ fontSize: '13px', color: '#595959', lineHeight: '1.6' }}>
              <div style={{ marginBottom: '6px', display: 'flex', alignItems: 'center' }}>
                <span style={{ color: '#8c8c8c', minWidth: '40px' }}>地址:</span>
                <span style={{ fontFamily: 'monospace', fontSize: '12px' }}>{alertSend.config.address || '未配置'}</span>
              </div>
              {alertSend.receive_type === 'kafka' && 'topic' in alertSend.config && (
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <span style={{ color: '#8c8c8c', minWidth: '40px' }}>Topic:</span>
                  <span
                    style={{
                      fontFamily: 'monospace',
                      fontSize: '12px',
                      backgroundColor: '#f6f6f6',
                      padding: '2px 6px',
                      borderRadius: '4px',
                    }}
                  >
                    {alertSend.config.topic || '未配置'}
                  </span>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className={formStyles.tabContent}>
      <Card
        title="告警发送配置"
        size="small"
        className="mb-4"
        extra={
          <Space>
            <Button type="primary" size="small" icon={<PlusOutlined />} onClick={onAddAlertSend}>
              新增发送
            </Button>
            <Button size="small" icon={<EyeOutlined />} onClick={onSelectAlertSend}>
              选择发送
            </Button>
          </Space>
        }
      >
        {alertSends.length === 0 ? (
          <div
            style={{
              textAlign: 'center',
              padding: '60px 20px',
              color: '#bfbfbf',
              backgroundColor: '#fafafa',
              borderRadius: '8px',
              border: '1px dashed #d9d9d9',
            }}
          >
            <div style={{ fontSize: '16px', marginBottom: '8px' }}>暂无告警发送配置</div>
            <div style={{ fontSize: '14px' }}>请点击"新增发送"或"选择发送"添加配置</div>
          </div>
        ) : (
          <div style={{ padding: '8px 0' }}>{alertSends.map((alertSend, index) => renderAlertSendItem(alertSend, index))}</div>
        )}
      </Card>
    </div>
  );
};

export default NotificationConfigTab;
