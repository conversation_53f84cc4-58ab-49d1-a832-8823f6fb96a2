import React from 'react';
import { Card, Button, Space } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';

// 导入重构后的模块
import type { AlertSend } from '../../types';
import { formStyles } from '../../styles';

interface NotificationConfigTabProps {
  alertSends: AlertSend[];
  onAlertSendsChange: (alertSends: AlertSend[]) => void;
  onAddAlertSend: () => void;
  onEditAlertSend: (index: number) => void;
  onSelectAlertSend: () => void;
}

/**
 * 告警发送配置标签页组件
 * 管理告警发送方式的配置
 */
const NotificationConfigTab: React.FC<NotificationConfigTabProps> = ({ alertSends, onAlertSendsChange, onAddAlertSend, onEditAlertSend, onSelectAlertSend }) => {
  const handleDeleteAlertSend = (index: number) => {
    const newAlertSends = alertSends.filter((_, i) => i !== index);
    onAlertSendsChange(newAlertSends);
  };

  const renderAlertSendItem = (alertSend: AlertSend, index: number) => {
    return (
      <div
        key={alertSend.id}
        style={{
          display: 'flex',
          alignItems: 'stretch',
          backgroundColor: '#fff',
          border: 'none',
          borderLeft: `4px solid ${alertSend.receive_type === 'kafka' ? '#1890ff' : '#52c41a'}`,
          borderRadius: '0 8px 8px 0',
          marginBottom: '1px',
          transition: 'all 0.2s ease',
          minHeight: '80px',
        }}
        onMouseEnter={e => {
          e.currentTarget.style.backgroundColor = '#fafafa';
          e.currentTarget.style.borderLeftWidth = '6px';
        }}
        onMouseLeave={e => {
          e.currentTarget.style.backgroundColor = '#fff';
          e.currentTarget.style.borderLeftWidth = '4px';
        }}
      >
        {/* 左侧图标区域 */}
        <div
          style={{
            width: '80px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: alertSend.receive_type === 'kafka' ? '#f0f9ff' : '#f0fff4',
            borderRadius: '0 0 8px 0',
          }}
        >
          <div
            style={{
              width: '40px',
              height: '40px',
              borderRadius: '8px',
              backgroundColor: alertSend.receive_type === 'kafka' ? '#1890ff' : '#52c41a',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#fff',
              fontWeight: 'bold',
              fontSize: '14px',
            }}
          >
            {alertSend.receive_type === 'kafka' ? 'K' : 'P'}
          </div>
        </div>

        {/* 中间内容区域 */}
        <div style={{ flex: 1, padding: '16px 20px', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
          {/* 标题行 */}
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
            <span style={{ fontSize: '16px', fontWeight: '600', color: '#262626', marginRight: '12px' }}>{alertSend.name}</span>
            <span
              style={{
                fontSize: '11px',
                fontWeight: '500',
                color: alertSend.receive_type === 'kafka' ? '#1890ff' : '#52c41a',
                backgroundColor: alertSend.receive_type === 'kafka' ? '#e6f7ff' : '#f6ffed',
                padding: '2px 8px',
                borderRadius: '12px',
                textTransform: 'uppercase',
                letterSpacing: '0.5px',
              }}
            >
              {alertSend.receive_type}
            </span>
          </div>

          {/* 配置信息 */}
          <div style={{ fontSize: '13px', color: '#8c8c8c' }}>
            {!alertSend.config ? (
              <span style={{ fontStyle: 'italic' }}>暂无配置信息</span>
            ) : (
              <div style={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: '16px' }}>
                <span>
                  <strong>地址:</strong> {alertSend.config.address || '未配置'}
                </span>
                {alertSend.receive_type === 'kafka' && 'topic' in alertSend.config && (
                  <span>
                    <strong>Topic:</strong> {alertSend.config.topic || '未配置'}
                  </span>
                )}
              </div>
            )}
          </div>
        </div>

        {/* 右侧操作区域 */}
        <div
          style={{
            width: '60px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderLeft: '1px solid #f0f0f0',
          }}
        >
          <Button
            type="text"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteAlertSend(index)}
            style={{
              width: '36px',
              height: '36px',
              borderRadius: '6px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            title="取消选择"
          />
        </div>
      </div>
    );
  };

  return (
    <div className={formStyles.tabContent}>
      <Card
        title="告警发送配置"
        size="small"
        className="mb-4"
        extra={
          <Space>
            <Button type="primary" size="small" icon={<PlusOutlined />} onClick={onAddAlertSend}>
              新增发送
            </Button>
            <Button size="small" icon={<EyeOutlined />} onClick={onSelectAlertSend}>
              选择发送
            </Button>
          </Space>
        }
      >
        {alertSends.length === 0 ? (
          <div
            style={{
              textAlign: 'center',
              padding: '60px 20px',
              color: '#bfbfbf',
              backgroundColor: '#fafafa',
              borderRadius: '8px',
              border: '1px dashed #d9d9d9',
            }}
          >
            <div style={{ fontSize: '16px', marginBottom: '8px' }}>暂无告警发送配置</div>
            <div style={{ fontSize: '14px' }}>请点击"新增发送"或"选择发送"添加配置</div>
          </div>
        ) : (
          <div style={{ padding: '8px 0' }}>{alertSends.map((alertSend, index) => renderAlertSendItem(alertSend, index))}</div>
        )}
      </Card>
    </div>
  );
};

export default NotificationConfigTab;
