import React from 'react';
import { Card, Button, Space } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';

// 导入重构后的模块
import type { AlertSend } from '../../types';
import { formStyles } from '../../styles';

interface NotificationConfigTabProps {
  alertSends: AlertSend[];
  onAlertSendsChange: (alertSends: AlertSend[]) => void;
  onAddAlertSend: () => void;
  onEditAlertSend: (index: number) => void;
  onSelectAlertSend: () => void;
}

/**
 * 告警发送配置标签页组件
 * 管理告警发送方式的配置
 */
const NotificationConfigTab: React.FC<NotificationConfigTabProps> = ({ alertSends, onAlertSendsChange, onAddAlertSend, onEditAlertSend, onSelectAlertSend }) => {
  const handleDeleteAlertSend = (index: number) => {
    const newAlertSends = alertSends.filter((_, i) => i !== index);
    onAlertSendsChange(newAlertSends);
  };

  const renderAlertSendItem = (alertSend: AlertSend, index: number) => {
    return (
      <div
        key={alertSend.id}
        style={{
          border: '1px solid #d9d9d9',
          borderRadius: '6px',
          padding: '16px',
          marginBottom: '12px',
          backgroundColor: '#fafafa',
        }}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <div style={{ flex: 1 }}>
            {/* 发送名称 */}
            <div style={{ marginBottom: '8px' }}>
              <span style={{ fontWeight: 'bold', fontSize: '14px' }}>{alertSend.name}</span>
            </div>

            {/* 发送类型 */}
            <div style={{ marginBottom: '8px' }}>
              <span style={{ color: '#666', marginRight: '8px' }}>发送类型:</span>
              <span
                style={{
                  color: alertSend.receive_type === 'kafka' ? '#1890ff' : '#52c41a',
                  fontWeight: 'bold',
                }}
              >
                {alertSend.receive_type.toUpperCase()}
              </span>
            </div>

            {/* 配置信息 */}
            <div>
              <span style={{ color: '#666', marginRight: '8px' }}>配置信息:</span>
              {!alertSend.config ? (
                <span style={{ fontSize: '12px', color: '#999' }}>暂无配置</span>
              ) : (
                <div style={{ fontSize: '12px', marginTop: '4px' }}>
                  <div>地址: {alertSend.config.address || '未配置'}</div>
                  {alertSend.receive_type === 'kafka' && 'topic' in alertSend.config && <div>Topic: {alertSend.config.topic || '未配置'}</div>}
                  {alertSend.config.username && <div>用户名: {alertSend.config.username}</div>}
                </div>
              )}
            </div>
          </div>

          {/* 操作按钮 */}
          <div>
            <Space size="small">
              <Button type="text" size="small" icon={<EditOutlined />} onClick={() => onEditAlertSend(index)}>
                编辑
              </Button>
              <Button type="text" size="small" danger icon={<DeleteOutlined />} onClick={() => handleDeleteAlertSend(index)}>
                删除
              </Button>
            </Space>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={formStyles.tabContent}>
      <Card
        title="告警发送配置"
        size="small"
        className="mb-4"
        extra={
          <Space>
            <Button type="primary" size="small" icon={<PlusOutlined />} onClick={onAddAlertSend}>
              新增发送
            </Button>
            <Button size="small" icon={<EyeOutlined />} onClick={onSelectAlertSend}>
              选择发送
            </Button>
          </Space>
        }
      >
        {alertSends.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>暂无告警发送配置，请点击"新增发送"或"选择发送"添加</div>
        ) : (
          <div>{alertSends.map((alertSend, index) => renderAlertSendItem(alertSend, index))}</div>
        )}
      </Card>
    </div>
  );
};

export default NotificationConfigTab;
