import React from 'react';
import { Card, Button, Space } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';

// 导入重构后的模块
import type { AlertSend } from '../../types';
import { formStyles } from '../../styles';

interface NotificationConfigTabProps {
  alertSends: AlertSend[];
  onAlertSendsChange: (alertSends: AlertSend[]) => void;
  onAddAlertSend: () => void;
  onEditAlertSend: (index: number) => void;
  onSelectAlertSend: () => void;
}

/**
 * 告警发送配置标签页组件
 * 管理告警发送方式的配置
 */
const NotificationConfigTab: React.FC<NotificationConfigTabProps> = ({ alertSends, onAlertSendsChange, onAddAlertSend, onEditAlertSend, onSelectAlertSend }) => {
  const handleDeleteAlertSend = (index: number) => {
    const newAlertSends = alertSends.filter((_, i) => i !== index);
    onAlertSendsChange(newAlertSends);
  };

  const renderAlertSendItem = (alertSend: AlertSend, index: number) => {
    return (
      <div
        key={alertSend.id}
        style={{
          display: 'flex',
          alignItems: 'center',
          padding: '12px 16px',
          marginBottom: '8px',
          backgroundColor: '#fff',
          border: '1px solid #e8e8e8',
          borderRadius: '8px',
          boxShadow: '0 1px 2px rgba(0,0,0,0.04)',
          transition: 'all 0.2s ease',
        }}
        onMouseEnter={e => {
          e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
          e.currentTarget.style.borderColor = '#d9d9d9';
        }}
        onMouseLeave={e => {
          e.currentTarget.style.boxShadow = '0 1px 2px rgba(0,0,0,0.04)';
          e.currentTarget.style.borderColor = '#e8e8e8';
        }}
      >
        {/* 类型标识 */}
        <div
          style={{
            width: '60px',
            height: '60px',
            borderRadius: '50%',
            backgroundColor: alertSend.receive_type === 'kafka' ? '#e6f7ff' : '#f6ffed',
            border: `2px solid ${alertSend.receive_type === 'kafka' ? '#1890ff' : '#52c41a'}`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: '16px',
            flexShrink: 0,
          }}
        >
          <span
            style={{
              color: alertSend.receive_type === 'kafka' ? '#1890ff' : '#52c41a',
              fontWeight: 'bold',
              fontSize: '12px',
            }}
          >
            {alertSend.receive_type.toUpperCase()}
          </span>
        </div>

        {/* 主要信息 */}
        <div style={{ flex: 1, minWidth: 0 }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
            <span style={{ fontWeight: 'bold', fontSize: '16px', color: '#262626' }}>{alertSend.name}</span>
          </div>

          <div style={{ fontSize: '13px', color: '#8c8c8c', lineHeight: '1.4' }}>
            {!alertSend.config ? (
              <span>暂无配置信息</span>
            ) : (
              <div>
                <span>地址: {alertSend.config.address || '未配置'}</span>
                {alertSend.receive_type === 'kafka' && 'topic' in alertSend.config && <span style={{ marginLeft: '12px' }}>Topic: {alertSend.config.topic || '未配置'}</span>}
              </div>
            )}
          </div>
        </div>

        {/* 操作按钮 */}
        <div style={{ flexShrink: 0, marginLeft: '16px' }}>
          <Button
            type="text"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteAlertSend(index)}
            style={{
              borderRadius: '6px',
              height: '32px',
              padding: '0 12px',
            }}
          >
            取消选择
          </Button>
        </div>
      </div>
    );
  };

  return (
    <div className={formStyles.tabContent}>
      <Card
        title="告警发送配置"
        size="small"
        className="mb-4"
        extra={
          <Space>
            <Button type="primary" size="small" icon={<PlusOutlined />} onClick={onAddAlertSend}>
              新增发送
            </Button>
            <Button size="small" icon={<EyeOutlined />} onClick={onSelectAlertSend}>
              选择发送
            </Button>
          </Space>
        }
      >
        {alertSends.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>暂无告警发送配置，请点击"新增发送"或"选择发送"添加</div>
        ) : (
          <div>{alertSends.map((alertSend, index) => renderAlertSendItem(alertSend, index))}</div>
        )}
      </Card>
    </div>
  );
};

export default NotificationConfigTab;
