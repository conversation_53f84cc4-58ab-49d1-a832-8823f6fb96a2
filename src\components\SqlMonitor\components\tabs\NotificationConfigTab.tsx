import React from 'react';
import { Card, Button, Space } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';

// 导入重构后的模块
import type { AlertSend } from '../../types';
import { formStyles } from '../../styles';

interface NotificationConfigTabProps {
  alertSends: AlertSend[];
  onAlertSendsChange: (alertSends: AlertSend[]) => void;
  onAddAlertSend: () => void;
  onEditAlertSend: (index: number) => void;
  onSelectAlertSend: () => void;
}

/**
 * 告警发送配置标签页组件
 * 管理告警发送方式的配置
 */
const NotificationConfigTab: React.FC<NotificationConfigTabProps> = ({ alertSends, onAlertSendsChange, onAddAlertSend, onEditAlertSend, onSelectAlertSend }) => {
  const handleDeleteAlertSend = (index: number) => {
    const newAlertSends = alertSends.filter((_, i) => i !== index);
    onAlertSendsChange(newAlertSends);
  };

  const renderAlertSendItem = (alertSend: AlertSend, index: number) => {
    return (
      <div
        key={alertSend.id}
        style={{
          backgroundColor: '#fff',
          border: '1px solid #e8e8e8',
          borderRadius: '4px',
          marginBottom: '16px',
          padding: '0',
          overflow: 'hidden',
        }}
      >
        {/* 头部 - 名称和操作 */}
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: '12px 16px',
            backgroundColor: '#fafafa',
            borderBottom: '1px solid #e8e8e8',
          }}
        >
          <span style={{ fontSize: '16px', fontWeight: '500', color: '#262626' }}>{alertSend.name}</span>
          <Button type="text" size="small" danger icon={<DeleteOutlined />} onClick={() => handleDeleteAlertSend(index)}>
            取消选择
          </Button>
        </div>

        {/* 内容区域 */}
        <div style={{ padding: '16px' }}>
          {/* 发送类型 */}
          <div style={{ marginBottom: '12px' }}>
            <div style={{ fontSize: '12px', color: '#8c8c8c', marginBottom: '4px' }}>发送类型</div>
            <div
              style={{
                display: 'inline-block',
                padding: '4px 12px',
                backgroundColor: alertSend.receive_type === 'kafka' ? '#e6f7ff' : '#f6ffed',
                color: alertSend.receive_type === 'kafka' ? '#1890ff' : '#52c41a',
                borderRadius: '4px',
                fontSize: '14px',
                fontWeight: '500',
              }}
            >
              {alertSend.receive_type.toUpperCase()}
            </div>
          </div>

          {/* 配置信息 */}
          {!alertSend.config ? (
            <div style={{ marginBottom: '12px' }}>
              <div style={{ fontSize: '12px', color: '#8c8c8c', marginBottom: '4px' }}>配置信息</div>
              <div style={{ fontSize: '14px', color: '#bfbfbf', fontStyle: 'italic' }}>暂无配置</div>
            </div>
          ) : (
            <>
              {/* 地址 */}
              <div style={{ marginBottom: '12px' }}>
                <div style={{ fontSize: '12px', color: '#8c8c8c', marginBottom: '4px' }}>连接地址</div>
                <div
                  style={{
                    fontSize: '14px',
                    color: '#262626',
                    fontFamily: 'monospace',
                    backgroundColor: '#f5f5f5',
                    padding: '6px 8px',
                    borderRadius: '4px',
                    border: '1px solid #e8e8e8',
                  }}
                >
                  {alertSend.config.address || '未配置'}
                </div>
              </div>

              {/* Kafka Topic */}
              {alertSend.receive_type === 'kafka' && 'topic' in alertSend.config && (
                <div style={{ marginBottom: '12px' }}>
                  <div style={{ fontSize: '12px', color: '#8c8c8c', marginBottom: '4px' }}>Kafka Topic</div>
                  <div
                    style={{
                      fontSize: '14px',
                      color: '#262626',
                      fontFamily: 'monospace',
                      backgroundColor: '#f5f5f5',
                      padding: '6px 8px',
                      borderRadius: '4px',
                      border: '1px solid #e8e8e8',
                    }}
                  >
                    {alertSend.config.topic || '未配置'}
                  </div>
                </div>
              )}

              {/* 用户名 */}
              {alertSend.config.username && (
                <div style={{ marginBottom: '12px' }}>
                  <div style={{ fontSize: '12px', color: '#8c8c8c', marginBottom: '4px' }}>用户名</div>
                  <div
                    style={{
                      fontSize: '14px',
                      color: '#262626',
                      backgroundColor: '#f5f5f5',
                      padding: '6px 8px',
                      borderRadius: '4px',
                      border: '1px solid #e8e8e8',
                    }}
                  >
                    {alertSend.config.username}
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className={formStyles.tabContent}>
      <Card
        title="告警发送配置"
        size="small"
        className="mb-4"
        extra={
          <Space>
            <Button type="primary" size="small" icon={<PlusOutlined />} onClick={onAddAlertSend}>
              新增发送
            </Button>
            <Button size="small" icon={<EyeOutlined />} onClick={onSelectAlertSend}>
              选择发送
            </Button>
          </Space>
        }
      >
        {alertSends.length === 0 ? (
          <div
            style={{
              textAlign: 'center',
              padding: '60px 20px',
              color: '#bfbfbf',
              backgroundColor: '#fafafa',
              borderRadius: '8px',
              border: '1px dashed #d9d9d9',
            }}
          >
            <div style={{ fontSize: '16px', marginBottom: '8px' }}>暂无告警发送配置</div>
            <div style={{ fontSize: '14px' }}>请点击"新增发送"或"选择发送"添加配置</div>
          </div>
        ) : (
          <div
            style={{
              backgroundColor: '#f8f9fa',
              borderRadius: '8px',
              padding: '8px',
              border: '1px solid #e9ecef',
            }}
          >
            {alertSends.map((alertSend, index) => renderAlertSendItem(alertSend, index))}
          </div>
        )}
      </Card>
    </div>
  );
};

export default NotificationConfigTab;
