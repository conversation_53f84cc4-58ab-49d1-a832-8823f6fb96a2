import React from 'react';
import { <PERSON><PERSON>, Space } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';

// 导入重构后的模块
import type { AlertSend } from '../../types';
import { formStyles } from '../../styles';

interface NotificationConfigTabProps {
  alertSends: AlertSend[];
  onAlertSendsChange: (alertSends: AlertSend[]) => void;
  onAddAlertSend: () => void;
  onEditAlertSend: (index: number) => void;
  onSelectAlertSend: () => void;
}

/**
 * 告警发送配置标签页组件
 * 管理告警发送方式的配置
 */
const NotificationConfigTab: React.FC<NotificationConfigTabProps> = ({ alertSends, onAlertSendsChange, onAddAlertSend, onEditAlertSend, onSelectAlertSend }) => {
  const handleDeleteAlertSend = (index: number) => {
    const newAlertSends = alertSends.filter((_, i) => i !== index);
    onAlertSendsChange(newAlertSends);
  };

  const renderAlertSendItem = (alertSend: AlertSend, index: number) => {
    return (
      <div
        key={alertSend.id}
        style={{
          backgroundColor: '#fff',
          border: '1px solid #e8e8e8',
          borderRadius: '8px',
          padding: '20px',
          position: 'relative',
          transition: 'all 0.2s ease',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
        }}
        onMouseEnter={e => {
          e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
          e.currentTarget.style.transform = 'translateY(-2px)';
        }}
        onMouseLeave={e => {
          e.currentTarget.style.boxShadow = '0 1px 3px rgba(0,0,0,0.1)';
          e.currentTarget.style.transform = 'translateY(0)';
        }}
      >
        {/* 操作按钮 - 右上角 */}
        <div style={{ position: 'absolute', top: '12px', right: '12px' }}>
          <Button
            type="text"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteAlertSend(index)}
            style={{
              width: '24px',
              height: '24px',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: 0,
            }}
            title="取消选择"
          />
        </div>

        {/* 名称 */}
        <div style={{ marginBottom: '16px', paddingRight: '40px' }}>
          <h3
            style={{
              margin: 0,
              fontSize: '18px',
              fontWeight: '600',
              color: '#262626',
              lineHeight: '1.4',
            }}
          >
            {alertSend.name}
          </h3>
        </div>

        {/* 字段网格 - 每行三个 */}
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(3, 1fr)',
            gap: '12px',
            marginBottom: '16px',
          }}
        >
          {/* 发送类型 */}
          <div>
            <div style={{ fontSize: '11px', color: '#8c8c8c', marginBottom: '4px', textTransform: 'uppercase', letterSpacing: '0.5px' }}>类型</div>
            <div
              style={{
                padding: '6px 10px',
                backgroundColor: alertSend.receive_type === 'kafka' ? '#e6f7ff' : '#f6ffed',
                color: alertSend.receive_type === 'kafka' ? '#1890ff' : '#52c41a',
                borderRadius: '4px',
                fontSize: '12px',
                fontWeight: '600',
                textAlign: 'center',
                textTransform: 'uppercase',
              }}
            >
              {alertSend.receive_type}
            </div>
          </div>

          {/* 连接状态 */}
          <div>
            <div style={{ fontSize: '11px', color: '#8c8c8c', marginBottom: '4px', textTransform: 'uppercase', letterSpacing: '0.5px' }}>状态</div>
            <div
              style={{
                padding: '6px 10px',
                backgroundColor: alertSend.config ? '#f6ffed' : '#fff2e8',
                color: alertSend.config ? '#52c41a' : '#fa8c16',
                borderRadius: '4px',
                fontSize: '12px',
                fontWeight: '600',
                textAlign: 'center',
              }}
            >
              {alertSend.config ? '已配置' : '未配置'}
            </div>
          </div>

          {/* Topic/用户名 */}
          <div>
            <div style={{ fontSize: '11px', color: '#8c8c8c', marginBottom: '4px', textTransform: 'uppercase', letterSpacing: '0.5px' }}>{alertSend.receive_type === 'kafka' ? 'Topic' : '用户名'}</div>
            <div
              style={{
                padding: '6px 10px',
                backgroundColor: '#f5f5f5',
                borderRadius: '4px',
                fontSize: '12px',
                color: '#595959',
                textAlign: 'center',
                fontFamily: 'monospace',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              {alertSend.receive_type === 'kafka' && alertSend.config && 'topic' in alertSend.config ? alertSend.config.topic || '未设置' : alertSend.config?.username || '未设置'}
            </div>
          </div>
        </div>

        {/* 地址信息 - 单独一行 */}
        {alertSend.config && (
          <div>
            <div style={{ fontSize: '11px', color: '#8c8c8c', marginBottom: '6px', textTransform: 'uppercase', letterSpacing: '0.5px' }}>连接地址</div>
            <div
              style={{
                padding: '8px 12px',
                backgroundColor: '#f8f9fa',
                border: '1px solid #e9ecef',
                borderRadius: '4px',
                fontSize: '12px',
                color: '#495057',
                fontFamily: 'monospace',
                wordBreak: 'break-all',
                lineHeight: '1.4',
              }}
            >
              {alertSend.config.address || '未配置'}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div
      className={formStyles.tabContent}
      style={{
        position: 'relative',
        height: '600px', // 设置固定高度
        display: 'flex',
        flexDirection: 'column',
        border: '1px solid #e8e8e8',
        borderRadius: '8px',
        backgroundColor: '#fff',
      }}
    >
      {/* 固定顶部 - 标题和操作按钮 */}
      <div
        style={{
          position: 'sticky',
          top: 0,
          zIndex: 10,
          backgroundColor: '#fff',
          borderBottom: '1px solid #e8e8e8',
          padding: '16px 20px',
          borderRadius: '8px 8px 0 0',
        }}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <h3 style={{ margin: 0, fontSize: '16px', fontWeight: '600', color: '#262626' }}>告警发送配置</h3>
            <span style={{ fontSize: '14px', color: '#8c8c8c' }}>共 {alertSends.length} 个配置</span>
            <span style={{ fontSize: '12px', color: '#bfbfbf' }}>
              ({alertSends.filter(item => item.config).length} 个已配置 / {alertSends.filter(item => !item.config).length} 个未配置)
            </span>
          </div>
          <Space>
            <Button type="primary" size="small" icon={<PlusOutlined />} onClick={onAddAlertSend}>
              新增发送
            </Button>
            <Button size="small" icon={<EyeOutlined />} onClick={onSelectAlertSend}>
              选择发送
            </Button>
          </Space>
        </div>
      </div>

      {/* 可滚动的中间内容区域 */}
      <div
        style={{
          flex: 1,
          overflowY: 'auto',
          overflowX: 'hidden',
          padding: '16px 20px',
          backgroundColor: '#f8f9fa',
          maxHeight: 'calc(600px - 140px)', // 减去顶部和底部的高度
        }}
      >
        {alertSends.length === 0 ? (
          <div
            style={{
              textAlign: 'center',
              padding: '60px 20px',
              color: '#bfbfbf',
              backgroundColor: '#fafafa',
              borderRadius: '8px',
              border: '1px dashed #d9d9d9',
            }}
          >
            <div style={{ fontSize: '16px', marginBottom: '8px' }}>暂无告警发送配置</div>
            <div style={{ fontSize: '14px' }}>请点击"新增发送"或"选择发送"添加配置</div>
          </div>
        ) : (
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(320px, 1fr))',
              gap: '16px',
              paddingBottom: '20px', // 底部留白，确保最后一项不被遮挡
            }}
          >
            {alertSends.map((alertSend, index) => renderAlertSendItem(alertSend, index))}
          </div>
        )}
      </div>
    </div>
  );
};

export default NotificationConfigTab;
